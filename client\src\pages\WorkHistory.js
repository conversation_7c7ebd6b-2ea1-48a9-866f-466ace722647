import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon,
  CheckCircleIcon, 
  ClockIcon, 
  XCircleIcon 
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';
import WorkHistoryForm from '../components/WorkHistoryForm';

const WorkHistory = () => {
  const [workHistory, setWorkHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingJob, setEditingJob] = useState(null);
  const [pagination, setPagination] = useState({});

  useEffect(() => {
    fetchWorkHistory();
  }, []);

  const fetchWorkHistory = async (page = 1) => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/work-history?page=${page}&limit=10`);
      setWorkHistory(response.data.data.workHistory);
      setPagination(response.data.data.pagination);
    } catch (error) {
      toast.error('Failed to load work history');
      console.error('Work history error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddJob = () => {
    setEditingJob(null);
    setShowForm(true);
  };

  const handleEditJob = (job) => {
    setEditingJob(job);
    setShowForm(true);
  };

  const handleDeleteJob = async (jobId) => {
    if (!window.confirm('Are you sure you want to delete this work experience?')) {
      return;
    }

    try {
      await axios.delete(`/api/work-history/${jobId}`);
      toast.success('Work experience deleted successfully');
      fetchWorkHistory();
    } catch (error) {
      toast.error('Failed to delete work experience');
      console.error('Delete error:', error);
    }
  };

  const handleFormSubmit = async (formData) => {
    try {
      if (editingJob) {
        await axios.put(`/api/work-history/${editingJob.id}`, formData);
        toast.success('Work experience updated successfully');
      } else {
        await axios.post('/api/work-history', formData);
        toast.success('Work experience added successfully');
      }
      
      setShowForm(false);
      setEditingJob(null);
      fetchWorkHistory();
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to save work experience';
      toast.error(message);
      throw error;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'not_verified':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'verified':
        return 'Verified';
      case 'pending':
        return 'Pending Verification';
      case 'not_verified':
        return 'Not Verified';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'text-green-800 bg-green-100';
      case 'pending':
        return 'text-yellow-800 bg-yellow-100';
      case 'not_verified':
        return 'text-red-800 bg-red-100';
      default:
        return 'text-gray-800 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Work History</h1>
          <p className="mt-2 text-gray-600">
            Manage your employment history and verification requests.
          </p>
        </div>
        <button
          onClick={handleAddJob}
          className="btn-primary inline-flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Work Experience
        </button>
      </div>

      {/* Work History List */}
      {workHistory.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6.5" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No work history yet</h3>
          <p className="text-gray-500 mb-6">
            Start by adding your employment history to begin the verification process.
          </p>
          <button
            onClick={handleAddJob}
            className="btn-primary inline-flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Your First Job
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {workHistory.map((job) => (
            <div key={job.id} className="card">
              <div className="card-body">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <div className="flex-shrink-0 mr-3">
                        {getStatusIcon(job.verificationStatus)}
                      </div>
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {job.jobTitle}
                        </h3>
                        <p className="text-gray-600">{job.companyName}</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div>
                        <p className="text-sm text-gray-500">Employment Period</p>
                        <p className="text-sm font-medium text-gray-900">
                          {new Date(job.startDate).toLocaleDateString()} - {' '}
                          {job.endDate ? new Date(job.endDate).toLocaleDateString() : 'Present'}
                        </p>
                      </div>
                      
                      <div>
                        <p className="text-sm text-gray-500">Employment Type</p>
                        <p className="text-sm font-medium text-gray-900 capitalize">
                          {job.employmentType?.replace('-', ' ')}
                        </p>
                      </div>
                      
                      {job.department && (
                        <div>
                          <p className="text-sm text-gray-500">Department</p>
                          <p className="text-sm font-medium text-gray-900">{job.department}</p>
                        </div>
                      )}
                      
                      {job.hrContactEmail && (
                        <div>
                          <p className="text-sm text-gray-500">HR Contact</p>
                          <p className="text-sm font-medium text-gray-900">{job.hrContactEmail}</p>
                        </div>
                      )}
                    </div>

                    {job.description && (
                      <div className="mt-4">
                        <p className="text-sm text-gray-500">Description</p>
                        <p className="text-sm text-gray-900 mt-1">{job.description}</p>
                      </div>
                    )}

                    <div className="mt-4 flex items-center justify-between">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(job.verificationStatus)}`}>
                        {getStatusText(job.verificationStatus)}
                      </span>
                      
                      {job.verificationRequestedAt && (
                        <p className="text-xs text-gray-500">
                          Verification requested: {new Date(job.verificationRequestedAt).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => handleEditJob(job)}
                      className="p-2 text-gray-400 hover:text-gray-600"
                      title="Edit"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDeleteJob(job.id)}
                      className="p-2 text-gray-400 hover:text-red-600"
                      title="Delete"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <nav className="flex space-x-2">
                {[...Array(pagination.totalPages)].map((_, i) => (
                  <button
                    key={i + 1}
                    onClick={() => fetchWorkHistory(i + 1)}
                    className={`px-3 py-2 text-sm font-medium rounded-md ${
                      pagination.currentPage === i + 1
                        ? 'bg-primary-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                    }`}
                  >
                    {i + 1}
                  </button>
                ))}
              </nav>
            </div>
          )}
        </div>
      )}

      {/* Work History Form Modal */}
      {showForm && (
        <WorkHistoryForm
          job={editingJob}
          onSubmit={handleFormSubmit}
          onCancel={() => {
            setShowForm(false);
            setEditingJob(null);
          }}
        />
      )}
    </div>
  );
};

export default WorkHistory;
