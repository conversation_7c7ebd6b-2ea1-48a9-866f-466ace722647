const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');

const Employer = sequelize.define('Employer', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  companyName: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [1, 100]
    }
  },
  hrContactEmail: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isEmail: true
    }
  },
  hrContactName: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [1, 100]
    }
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [10, 20]
    }
  },
  address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  website: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isUrl: true
    }
  },
  industry: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [1, 50]
    }
  },
  companySize: {
    type: DataTypes.ENUM('1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'),
    allowNull: true
  },
  isPilotTester: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  verificationCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lastVerificationAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'employers',
  indexes: [
    {
      unique: true,
      fields: ['company_name', 'hr_contact_email']
    },
    {
      fields: ['is_pilot_tester']
    },
    {
      fields: ['is_active']
    }
  ]
});

// Instance methods
Employer.prototype.incrementVerificationCount = async function() {
  this.verificationCount += 1;
  this.lastVerificationAt = new Date();
  await this.save();
};

module.exports = Employer;
