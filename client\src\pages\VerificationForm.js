import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  ClockIcon 
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const VerificationForm = () => {
  const { token } = useParams();
  const [verificationData, setVerificationData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState(null);
  
  const [formData, setFormData] = useState({
    employmentConfirmed: null,
    verifiedJobTitle: '',
    verifiedStartDate: '',
    verifiedEndDate: '',
    verifiedEmploymentType: '',
    verifierName: '',
    verifierTitle: '',
    verifierEmail: '',
    comments: '',
    discrepancies: ''
  });
  const [formErrors, setFormErrors] = useState({});

  useEffect(() => {
    fetchVerificationData();
  }, [token]);

  const fetchVerificationData = async () => {
    try {
      const response = await axios.get(`/api/verification/${token}`);
      const data = response.data.data;
      
      setVerificationData(data);
      
      // Pre-fill form with candidate's claimed information
      setFormData(prev => ({
        ...prev,
        verifiedJobTitle: data.employment.jobTitle,
        verifiedStartDate: data.employment.startDate,
        verifiedEndDate: data.employment.endDate || '',
        verifiedEmploymentType: data.employment.employmentType
      }));
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to load verification form';
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type } = e.target;
    
    if (type === 'radio') {
      setFormData(prev => ({
        ...prev,
        [name]: value === 'true'
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Clear field error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (formData.employmentConfirmed === null) {
      newErrors.employmentConfirmed = 'Please confirm whether the employment information is accurate';
    }

    if (!formData.verifierName.trim()) {
      newErrors.verifierName = 'Verifier name is required';
    }

    if (!formData.verifierEmail.trim()) {
      newErrors.verifierEmail = 'Verifier email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.verifierEmail)) {
      newErrors.verifierEmail = 'Please enter a valid email address';
    }

    if (formData.employmentConfirmed && !formData.verifiedJobTitle.trim()) {
      newErrors.verifiedJobTitle = 'Job title is required when confirming employment';
    }

    if (formData.employmentConfirmed && !formData.verifiedStartDate) {
      newErrors.verifiedStartDate = 'Start date is required when confirming employment';
    }

    setFormErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSubmitting(true);

    try {
      await axios.post(`/api/verification/${token}`, formData);
      setSubmitted(true);
      toast.success('Verification submitted successfully');
    } catch (error) {
      const message = error.response?.data?.message || 'Failed to submit verification';
      toast.error(message);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <div className="card">
            <div className="card-body text-center">
              <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <h2 className="text-lg font-medium text-gray-900 mb-2">
                Verification Form Error
              </h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <p className="text-sm text-gray-500">
                If you believe this is an error, please contact the candidate or VeriJob support.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (submitted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full">
          <div className="card">
            <div className="card-body text-center">
              <CheckCircleIcon className="mx-auto h-12 w-12 text-green-500 mb-4" />
              <h2 className="text-lg font-medium text-gray-900 mb-2">
                Verification Submitted
              </h2>
              <p className="text-gray-600 mb-4">
                Thank you for completing the employment verification. The candidate will be notified of your response.
              </p>
              <p className="text-sm text-gray-500">
                You can now close this window.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100 mb-4">
            <span className="text-2xl font-bold text-primary-600">V</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Employment Verification</h1>
          <p className="mt-2 text-gray-600">
            Please verify the employment information for the candidate below.
          </p>
        </div>

        {/* Verification Info */}
        <div className="card mb-8">
          <div className="card-header">
            <h2 className="text-lg font-medium text-gray-900">Verification Request Details</h2>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Candidate</h3>
                <p className="mt-1 text-sm text-gray-900">{verificationData?.candidate.name}</p>
                <p className="text-sm text-gray-500">{verificationData?.candidate.email}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500">Verification Expires</h3>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(verificationData?.verification.expiresAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Employment Information */}
        <div className="card mb-8">
          <div className="card-header">
            <h2 className="text-lg font-medium text-gray-900">Claimed Employment Information</h2>
            <p className="text-sm text-gray-500">
              Please verify the accuracy of the following employment details:
            </p>
          </div>
          <div className="card-body">
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <dt className="text-sm font-medium text-gray-500">Company</dt>
                <dd className="mt-1 text-sm text-gray-900">{verificationData?.employment.companyName}</dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Job Title</dt>
                <dd className="mt-1 text-sm text-gray-900">{verificationData?.employment.jobTitle}</dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Department</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {verificationData?.employment.department || 'Not specified'}
                </dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Employment Type</dt>
                <dd className="mt-1 text-sm text-gray-900 capitalize">
                  {verificationData?.employment.employmentType?.replace('-', ' ')}
                </dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">Start Date</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {new Date(verificationData?.employment.startDate).toLocaleDateString()}
                </dd>
              </div>
              
              <div>
                <dt className="text-sm font-medium text-gray-500">End Date</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {verificationData?.employment.endDate 
                    ? new Date(verificationData.employment.endDate).toLocaleDateString()
                    : 'Current Position'
                  }
                </dd>
              </div>
            </dl>
            
            {verificationData?.employment.description && (
              <div className="mt-6">
                <dt className="text-sm font-medium text-gray-500">Job Description</dt>
                <dd className="mt-1 text-sm text-gray-900">{verificationData.employment.description}</dd>
              </div>
            )}
          </div>
        </div>

        {/* Verification Form */}
        <form onSubmit={handleSubmit} className="card">
          <div className="card-header">
            <h2 className="text-lg font-medium text-gray-900">Verification Response</h2>
          </div>
          
          <div className="card-body space-y-6">
            {/* Employment Confirmation */}
            <div>
              <fieldset>
                <legend className="text-sm font-medium text-gray-900 mb-3">
                  Is the employment information above accurate? *
                </legend>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="employmentConfirmed"
                      value="true"
                      checked={formData.employmentConfirmed === true}
                      onChange={handleChange}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="ml-2 text-sm text-gray-900">
                      Yes, the information is accurate
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="employmentConfirmed"
                      value="false"
                      checked={formData.employmentConfirmed === false}
                      onChange={handleChange}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="ml-2 text-sm text-gray-900">
                      No, there are inaccuracies
                    </span>
                  </label>
                </div>
                {formErrors.employmentConfirmed && (
                  <p className="form-error">{formErrors.employmentConfirmed}</p>
                )}
              </fieldset>
            </div>

            {/* Corrected Information (if employment not confirmed) */}
            {formData.employmentConfirmed === false && (
              <div className="border-l-4 border-yellow-400 bg-yellow-50 p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Please provide the correct information below
                    </h3>
                  </div>
                </div>
              </div>
            )}

            {/* Verified Information Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="verifiedJobTitle" className="form-label">
                  Correct Job Title {formData.employmentConfirmed && '*'}
                </label>
                <input
                  type="text"
                  id="verifiedJobTitle"
                  name="verifiedJobTitle"
                  className={`form-input ${formErrors.verifiedJobTitle ? 'border-red-300' : ''}`}
                  value={formData.verifiedJobTitle}
                  onChange={handleChange}
                />
                {formErrors.verifiedJobTitle && (
                  <p className="form-error">{formErrors.verifiedJobTitle}</p>
                )}
              </div>

              <div>
                <label htmlFor="verifiedEmploymentType" className="form-label">
                  Correct Employment Type
                </label>
                <select
                  id="verifiedEmploymentType"
                  name="verifiedEmploymentType"
                  className="form-input"
                  value={formData.verifiedEmploymentType}
                  onChange={handleChange}
                >
                  <option value="">Select employment type</option>
                  <option value="full-time">Full-time</option>
                  <option value="part-time">Part-time</option>
                  <option value="contract">Contract</option>
                  <option value="internship">Internship</option>
                  <option value="freelance">Freelance</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="verifiedStartDate" className="form-label">
                  Correct Start Date {formData.employmentConfirmed && '*'}
                </label>
                <input
                  type="date"
                  id="verifiedStartDate"
                  name="verifiedStartDate"
                  className={`form-input ${formErrors.verifiedStartDate ? 'border-red-300' : ''}`}
                  value={formData.verifiedStartDate}
                  onChange={handleChange}
                />
                {formErrors.verifiedStartDate && (
                  <p className="form-error">{formErrors.verifiedStartDate}</p>
                )}
              </div>

              <div>
                <label htmlFor="verifiedEndDate" className="form-label">
                  Correct End Date (if applicable)
                </label>
                <input
                  type="date"
                  id="verifiedEndDate"
                  name="verifiedEndDate"
                  className="form-input"
                  value={formData.verifiedEndDate}
                  onChange={handleChange}
                />
              </div>
            </div>

            {/* Verifier Information */}
            <div className="border-t pt-6">
              <h3 className="text-md font-medium text-gray-900 mb-4">Verifier Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="verifierName" className="form-label">
                    Your Name *
                  </label>
                  <input
                    type="text"
                    id="verifierName"
                    name="verifierName"
                    required
                    className={`form-input ${formErrors.verifierName ? 'border-red-300' : ''}`}
                    value={formData.verifierName}
                    onChange={handleChange}
                  />
                  {formErrors.verifierName && (
                    <p className="form-error">{formErrors.verifierName}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="verifierTitle" className="form-label">
                    Your Job Title
                  </label>
                  <input
                    type="text"
                    id="verifierTitle"
                    name="verifierTitle"
                    className="form-input"
                    value={formData.verifierTitle}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div className="mt-4">
                <label htmlFor="verifierEmail" className="form-label">
                  Your Email Address *
                </label>
                <input
                  type="email"
                  id="verifierEmail"
                  name="verifierEmail"
                  required
                  className={`form-input ${formErrors.verifierEmail ? 'border-red-300' : ''}`}
                  value={formData.verifierEmail}
                  onChange={handleChange}
                />
                {formErrors.verifierEmail && (
                  <p className="form-error">{formErrors.verifierEmail}</p>
                )}
              </div>
            </div>

            {/* Comments */}
            <div>
              <label htmlFor="comments" className="form-label">
                Additional Comments
              </label>
              <textarea
                id="comments"
                name="comments"
                rows={3}
                className="form-input"
                value={formData.comments}
                onChange={handleChange}
                placeholder="Any additional information about the candidate's employment..."
              />
            </div>

            {/* Discrepancies */}
            {formData.employmentConfirmed === false && (
              <div>
                <label htmlFor="discrepancies" className="form-label">
                  Please describe the discrepancies
                </label>
                <textarea
                  id="discrepancies"
                  name="discrepancies"
                  rows={3}
                  className="form-input"
                  value={formData.discrepancies}
                  onChange={handleChange}
                  placeholder="Describe what information is incorrect and provide the correct details..."
                />
              </div>
            )}
          </div>

          <div className="card-footer">
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={submitting}
                className="btn-primary"
              >
                {submitting ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </div>
                ) : (
                  'Submit Verification'
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VerificationForm;
