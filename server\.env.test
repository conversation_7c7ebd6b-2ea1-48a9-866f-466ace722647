# Test Environment Configuration
NODE_ENV=test
PORT=5001
API_BASE_URL=http://localhost:5001
CLIENT_BASE_URL=http://localhost:3000

# Test Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=verijob_test
DB_USER=postgres
DB_PASSWORD=test_password

# Test JWT Configuration
JWT_SECRET=test-super-secret-jwt-key-for-testing-only
JWT_EXPIRES_IN=1h

# Test Email Configuration (Mock)
SENDGRID_API_KEY=test-sendgrid-key
FROM_EMAIL=<EMAIL>
FROM_NAME=VeriJob Test System

# Test Security
SESSION_SECRET=test-session-secret
CSRF_SECRET=test-csrf-secret

# Test Verification Settings
VERIFICATION_LINK_EXPIRES_DAYS=7
MAX_VERIFICATION_ATTEMPTS=3

# Test Rate Limiting (More lenient for tests)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
