import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

const WorkHistoryForm = ({ job, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    companyName: '',
    jobTitle: '',
    department: '',
    startDate: '',
    endDate: '',
    isCurrentJob: false,
    employmentType: 'full-time',
    salary: '',
    salaryType: 'yearly',
    description: '',
    hrContactEmail: '',
    hrContactName: '',
    supervisorName: '',
    supervisorEmail: ''
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (job) {
      setFormData({
        companyName: job.companyName || '',
        jobTitle: job.jobTitle || '',
        department: job.department || '',
        startDate: job.startDate || '',
        endDate: job.endDate || '',
        isCurrentJob: job.isCurrentJob || false,
        employmentType: job.employmentType || 'full-time',
        salary: job.salary || '',
        salaryType: job.salaryType || 'yearly',
        description: job.description || '',
        hrContactEmail: job.hrContactEmail || '',
        hrContactName: job.hrContactName || '',
        supervisorName: job.supervisorName || '',
        supervisorEmail: job.supervisorEmail || ''
      });
    }
  }, [job]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Clear end date if current job is checked
    if (name === 'isCurrentJob' && checked) {
      setFormData(prev => ({
        ...prev,
        endDate: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required';
    }

    if (!formData.jobTitle.trim()) {
      newErrors.jobTitle = 'Job title is required';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    }

    if (!formData.isCurrentJob && !formData.endDate) {
      newErrors.endDate = 'End date is required for past positions';
    }

    if (formData.endDate && formData.startDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
      newErrors.endDate = 'End date must be after start date';
    }

    if (formData.hrContactEmail && !/\S+@\S+\.\S+/.test(formData.hrContactEmail)) {
      newErrors.hrContactEmail = 'Please enter a valid email address';
    }

    if (formData.supervisorEmail && !/\S+@\S+\.\S+/.test(formData.supervisorEmail)) {
      newErrors.supervisorEmail = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Clean up data
      const submitData = { ...formData };
      if (submitData.isCurrentJob) {
        submitData.endDate = null;
      }
      if (submitData.salary === '') {
        submitData.salary = null;
      }

      await onSubmit(submitData);
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            {job ? 'Edit Work Experience' : 'Add Work Experience'}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Company and Job Title */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="companyName" className="form-label">
                Company Name *
              </label>
              <input
                type="text"
                id="companyName"
                name="companyName"
                required
                className={`form-input ${errors.companyName ? 'border-red-300' : ''}`}
                value={formData.companyName}
                onChange={handleChange}
              />
              {errors.companyName && (
                <p className="form-error">{errors.companyName}</p>
              )}
            </div>

            <div>
              <label htmlFor="jobTitle" className="form-label">
                Job Title *
              </label>
              <input
                type="text"
                id="jobTitle"
                name="jobTitle"
                required
                className={`form-input ${errors.jobTitle ? 'border-red-300' : ''}`}
                value={formData.jobTitle}
                onChange={handleChange}
              />
              {errors.jobTitle && (
                <p className="form-error">{errors.jobTitle}</p>
              )}
            </div>
          </div>

          {/* Department and Employment Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="department" className="form-label">
                Department
              </label>
              <input
                type="text"
                id="department"
                name="department"
                className="form-input"
                value={formData.department}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="employmentType" className="form-label">
                Employment Type
              </label>
              <select
                id="employmentType"
                name="employmentType"
                className="form-input"
                value={formData.employmentType}
                onChange={handleChange}
              >
                <option value="full-time">Full-time</option>
                <option value="part-time">Part-time</option>
                <option value="contract">Contract</option>
                <option value="internship">Internship</option>
                <option value="freelance">Freelance</option>
              </select>
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="startDate" className="form-label">
                Start Date *
              </label>
              <input
                type="date"
                id="startDate"
                name="startDate"
                required
                className={`form-input ${errors.startDate ? 'border-red-300' : ''}`}
                value={formData.startDate}
                onChange={handleChange}
              />
              {errors.startDate && (
                <p className="form-error">{errors.startDate}</p>
              )}
            </div>

            <div>
              <label htmlFor="endDate" className="form-label">
                End Date
              </label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                disabled={formData.isCurrentJob}
                className={`form-input ${errors.endDate ? 'border-red-300' : ''} ${formData.isCurrentJob ? 'bg-gray-100' : ''}`}
                value={formData.endDate}
                onChange={handleChange}
              />
              {errors.endDate && (
                <p className="form-error">{errors.endDate}</p>
              )}
            </div>
          </div>

          {/* Current Job Checkbox */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isCurrentJob"
              name="isCurrentJob"
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              checked={formData.isCurrentJob}
              onChange={handleChange}
            />
            <label htmlFor="isCurrentJob" className="ml-2 block text-sm text-gray-900">
              This is my current job
            </label>
          </div>

          {/* Salary */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="salary" className="form-label">
                Salary (Optional)
              </label>
              <input
                type="number"
                id="salary"
                name="salary"
                className="form-input"
                value={formData.salary}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="salaryType" className="form-label">
                Salary Type
              </label>
              <select
                id="salaryType"
                name="salaryType"
                className="form-input"
                value={formData.salaryType}
                onChange={handleChange}
              >
                <option value="hourly">Hourly</option>
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="form-label">
              Job Description
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              className="form-input"
              value={formData.description}
              onChange={handleChange}
              placeholder="Brief description of your role and responsibilities..."
            />
          </div>

          {/* HR Contact Information */}
          <div className="border-t pt-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">
              HR Contact Information (For Verification)
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="hrContactName" className="form-label">
                  HR Contact Name
                </label>
                <input
                  type="text"
                  id="hrContactName"
                  name="hrContactName"
                  className="form-input"
                  value={formData.hrContactName}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label htmlFor="hrContactEmail" className="form-label">
                  HR Contact Email
                </label>
                <input
                  type="email"
                  id="hrContactEmail"
                  name="hrContactEmail"
                  className={`form-input ${errors.hrContactEmail ? 'border-red-300' : ''}`}
                  value={formData.hrContactEmail}
                  onChange={handleChange}
                />
                {errors.hrContactEmail && (
                  <p className="form-error">{errors.hrContactEmail}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <label htmlFor="supervisorName" className="form-label">
                  Supervisor Name
                </label>
                <input
                  type="text"
                  id="supervisorName"
                  name="supervisorName"
                  className="form-input"
                  value={formData.supervisorName}
                  onChange={handleChange}
                />
              </div>

              <div>
                <label htmlFor="supervisorEmail" className="form-label">
                  Supervisor Email
                </label>
                <input
                  type="email"
                  id="supervisorEmail"
                  name="supervisorEmail"
                  className={`form-input ${errors.supervisorEmail ? 'border-red-300' : ''}`}
                  value={formData.supervisorEmail}
                  onChange={handleChange}
                />
                {errors.supervisorEmail && (
                  <p className="form-error">{errors.supervisorEmail}</p>
                )}
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="btn-outline"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {job ? 'Updating...' : 'Adding...'}
                </div>
              ) : (
                job ? 'Update Experience' : 'Add Experience'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default WorkHistoryForm;
