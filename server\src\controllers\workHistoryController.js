const { WorkHistory, Employer, Verification, User } = require('../models');
const { Op } = require('sequelize');
const emailService = require('../services/emailService');

// @desc    Get user's work history
// @route   GET /api/work-history
// @access  Private
const getWorkHistory = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = { userId: req.user.id };
    
    if (status) {
      whereClause.verificationStatus = status;
    }

    const workHistory = await WorkHistory.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Employer,
          as: 'employer',
          attributes: ['id', 'companyName', 'hrContactEmail', 'isPilotTester']
        },
        {
          model: Verification,
          as: 'verifications',
          attributes: ['id', 'status', 'emailSentAt', 'completedAt'],
          order: [['createdAt', 'DESC']],
          limit: 1
        }
      ],
      order: [['startDate', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        workHistory: workHistory.rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(workHistory.count / limit),
          totalItems: workHistory.count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get specific work history entry
// @route   GET /api/work-history/:id
// @access  Private
const getWorkHistoryById = async (req, res, next) => {
  try {
    const workHistory = await WorkHistory.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      },
      include: [
        {
          model: Employer,
          as: 'employer',
          attributes: ['id', 'companyName', 'hrContactEmail', 'isPilotTester']
        },
        {
          model: Verification,
          as: 'verifications',
          order: [['createdAt', 'DESC']]
        }
      ]
    });

    if (!workHistory) {
      return res.status(404).json({
        success: false,
        message: 'Work history entry not found'
      });
    }

    res.json({
      success: true,
      data: { workHistory }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new work history entry
// @route   POST /api/work-history
// @access  Private
const createWorkHistory = async (req, res, next) => {
  try {
    const workHistoryData = {
      ...req.body,
      userId: req.user.id
    };

    // Check if employer exists, if not create one
    if (workHistoryData.hrContactEmail) {
      const [employer] = await Employer.findOrCreate({
        where: {
          companyName: workHistoryData.companyName,
          hrContactEmail: workHistoryData.hrContactEmail
        },
        defaults: {
          companyName: workHistoryData.companyName,
          hrContactEmail: workHistoryData.hrContactEmail,
          hrContactName: workHistoryData.hrContactName
        }
      });
      
      workHistoryData.employerId = employer.id;
    }

    const workHistory = await WorkHistory.create(workHistoryData);

    // Trigger verification process
    await triggerVerification(workHistory);

    // Fetch the created work history with associations
    const createdWorkHistory = await WorkHistory.findByPk(workHistory.id, {
      include: [
        {
          model: Employer,
          as: 'employer',
          attributes: ['id', 'companyName', 'hrContactEmail', 'isPilotTester']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Work history created and verification email sent',
      data: { workHistory: createdWorkHistory }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update work history entry
// @route   PUT /api/work-history/:id
// @access  Private
const updateWorkHistory = async (req, res, next) => {
  try {
    const workHistory = await WorkHistory.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      }
    });

    if (!workHistory) {
      return res.status(404).json({
        success: false,
        message: 'Work history entry not found'
      });
    }

    // Check if significant fields changed that require re-verification
    const significantFields = ['companyName', 'jobTitle', 'startDate', 'endDate', 'hrContactEmail'];
    const hasSignificantChanges = significantFields.some(field => 
      req.body[field] && req.body[field] !== workHistory[field]
    );

    // Update work history
    await workHistory.update(req.body);

    // If significant changes, trigger new verification
    if (hasSignificantChanges) {
      workHistory.verificationStatus = 'pending';
      workHistory.verificationRequestedAt = null;
      workHistory.verificationCompletedAt = null;
      await workHistory.save();

      // Trigger new verification
      await triggerVerification(workHistory);
    }

    // Fetch updated work history with associations
    const updatedWorkHistory = await WorkHistory.findByPk(workHistory.id, {
      include: [
        {
          model: Employer,
          as: 'employer',
          attributes: ['id', 'companyName', 'hrContactEmail', 'isPilotTester']
        }
      ]
    });

    res.json({
      success: true,
      message: hasSignificantChanges ? 
        'Work history updated and new verification email sent' : 
        'Work history updated successfully',
      data: { workHistory: updatedWorkHistory }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete work history entry
// @route   DELETE /api/work-history/:id
// @access  Private
const deleteWorkHistory = async (req, res, next) => {
  try {
    const workHistory = await WorkHistory.findOne({
      where: {
        id: req.params.id,
        userId: req.user.id
      }
    });

    if (!workHistory) {
      return res.status(404).json({
        success: false,
        message: 'Work history entry not found'
      });
    }

    await workHistory.destroy();

    res.json({
      success: true,
      message: 'Work history entry deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to trigger verification
const triggerVerification = async (workHistory) => {
  try {
    if (!workHistory.hrContactEmail) {
      return;
    }

    // Get candidate information
    const candidate = await User.findByPk(workHistory.userId);
    if (!candidate) {
      throw new Error('Candidate not found');
    }

    // Create verification record
    const verification = await Verification.create({
      workHistoryId: workHistory.id,
      employerId: workHistory.employerId,
      emailSentTo: workHistory.hrContactEmail
    });

    // Update work history status
    workHistory.verificationStatus = 'pending';
    workHistory.verificationRequestedAt = new Date();
    await workHistory.save();

    // Send verification email
    try {
      await emailService.sendVerificationRequest(verification, workHistory, candidate);
      console.log(`Verification email sent to: ${workHistory.hrContactEmail}`);
    } catch (emailError) {
      console.error('Error sending verification email:', emailError);
      // Don't throw error - verification record is still created
    }

    console.log(`Verification token: ${verification.token}`);
  } catch (error) {
    console.error('Error triggering verification:', error);
  }
};

module.exports = {
  getWorkHistory,
  createWorkHistory,
  updateWorkHistory,
  deleteWorkHistory,
  getWorkHistoryById
};
