const { User, Employer, WorkHistory, Verification } = require('../models');
const bcrypt = require('bcryptjs');

const seedData = async () => {
  try {
    console.log('Starting database seeding...');

    // Create admin user
    const adminUser = await User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        emailVerified: true
      }
    });

    console.log('Admin user created:', adminUser[0].email);

    // Create sample candidate
    const candidate = await User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+1234567890',
        role: 'candidate',
        emailVerified: true
      }
    });

    console.log('Sample candidate created:', candidate[0].email);

    // Create sample employers
    const employers = [
      {
        companyName: 'Tech Corp Inc.',
        hrContactEmail: '<EMAIL>',
        hrContactName: 'Sarah <PERSON>',
        phone: '+1234567891',
        industry: 'Technology',
        companySize: '201-500',
        isPilotTester: true
      },
      {
        companyName: 'Marketing Solutions LLC',
        hrContactEmail: '<EMAIL>',
        hrContactName: 'Mike Wilson',
        phone: '+1234567892',
        industry: 'Marketing',
        companySize: '51-200',
        isPilotTester: true
      },
      {
        companyName: 'Finance Partners',
        hrContactEmail: '<EMAIL>',
        hrContactName: 'Lisa Chen',
        phone: '+1234567893',
        industry: 'Finance',
        companySize: '11-50',
        isPilotTester: false
      }
    ];

    const createdEmployers = [];
    for (const employerData of employers) {
      const [employer] = await Employer.findOrCreate({
        where: { 
          companyName: employerData.companyName,
          hrContactEmail: employerData.hrContactEmail 
        },
        defaults: employerData
      });
      createdEmployers.push(employer);
      console.log('Employer created:', employer.companyName);
    }

    // Create sample work history for the candidate
    const workHistoryData = [
      {
        userId: candidate[0].id,
        employerId: createdEmployers[0].id,
        companyName: 'Tech Corp Inc.',
        jobTitle: 'Software Developer',
        department: 'Engineering',
        startDate: '2022-01-15',
        endDate: '2023-06-30',
        employmentType: 'full-time',
        salary: 75000,
        salaryType: 'yearly',
        description: 'Developed web applications using React and Node.js',
        hrContactEmail: '<EMAIL>',
        hrContactName: 'Sarah Johnson',
        supervisorName: 'David Smith',
        supervisorEmail: '<EMAIL>',
        verificationStatus: 'pending'
      },
      {
        userId: candidate[0].id,
        employerId: createdEmployers[1].id,
        companyName: 'Marketing Solutions LLC',
        jobTitle: 'Digital Marketing Specialist',
        department: 'Marketing',
        startDate: '2023-07-01',
        isCurrentJob: true,
        employmentType: 'full-time',
        salary: 65000,
        salaryType: 'yearly',
        description: 'Managing digital marketing campaigns and social media',
        hrContactEmail: '<EMAIL>',
        hrContactName: 'Mike Wilson',
        supervisorName: 'Jennifer Brown',
        supervisorEmail: '<EMAIL>',
        verificationStatus: 'pending'
      }
    ];

    for (const workData of workHistoryData) {
      const [workHistory] = await WorkHistory.findOrCreate({
        where: { 
          userId: workData.userId,
          companyName: workData.companyName,
          jobTitle: workData.jobTitle,
          startDate: workData.startDate
        },
        defaults: workData
      });
      console.log('Work history created:', `${workHistory.jobTitle} at ${workHistory.companyName}`);
    }

    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Seeding failed:', error);
    throw error;
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedData()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = { seedData };
