const { body, param, query, validationResult } = require('express-validator');

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  
  next();
};

// User registration validation
const validateUserRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must be less than 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must be less than 50 characters'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  handleValidationErrors
];

// User login validation
const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  handleValidationErrors
];

// Work history validation
const validateWorkHistory = [
  body('companyName')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Company name is required and must be less than 100 characters'),
  body('jobTitle')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Job title is required and must be less than 100 characters'),
  body('startDate')
    .isISO8601()
    .withMessage('Please provide a valid start date'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid end date'),
  body('isCurrentJob')
    .optional()
    .isBoolean()
    .withMessage('isCurrentJob must be a boolean'),
  body('employmentType')
    .optional()
    .isIn(['full-time', 'part-time', 'contract', 'internship', 'freelance'])
    .withMessage('Invalid employment type'),
  body('hrContactEmail')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid HR contact email'),
  handleValidationErrors
];

// Employer validation
const validateEmployer = [
  body('companyName')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Company name is required and must be less than 100 characters'),
  body('hrContactEmail')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid HR contact email'),
  body('hrContactName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('HR contact name must be less than 100 characters'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('companySize')
    .optional()
    .isIn(['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'])
    .withMessage('Invalid company size'),
  handleValidationErrors
];

// Verification form validation
const validateVerificationForm = [
  body('employmentConfirmed')
    .isBoolean()
    .withMessage('Employment confirmation is required'),
  body('verifiedJobTitle')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Job title must be less than 100 characters'),
  body('verifiedStartDate')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid start date'),
  body('verifiedEndDate')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid end date'),
  body('verifierName')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Verifier name is required and must be less than 100 characters'),
  body('verifierTitle')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Verifier title must be less than 100 characters'),
  body('verifierEmail')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid verifier email'),
  body('comments')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Comments must be less than 1000 characters'),
  handleValidationErrors
];

// UUID parameter validation
const validateUUID = (paramName) => [
  param(paramName)
    .isUUID()
    .withMessage(`${paramName} must be a valid UUID`),
  handleValidationErrors
];

module.exports = {
  handleValidationErrors,
  validateUserRegistration,
  validateUserLogin,
  validateWorkHistory,
  validateEmployer,
  validateVerificationForm,
  validateUUID
};
