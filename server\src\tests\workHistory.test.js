const request = require('supertest');
const app = require('../index');
const { User, WorkHistory, Employer, sequelize } = require('../models');
const { generateToken } = require('../middleware/auth');

describe('Work History Endpoints', () => {
  let user;
  let accessToken;

  beforeAll(async () => {
    await sequelize.sync({ force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    // Clean up
    await WorkHistory.destroy({ where: {} });
    await Employer.destroy({ where: {} });
    await User.destroy({ where: {} });

    // Create test user
    user = await User.create({
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'password123',
      emailVerified: true
    });

    accessToken = generateToken(user.id);
  });

  describe('GET /api/work-history', () => {
    it('should get user work history', async () => {
      // Create test work history
      await WorkHistory.create({
        userId: user.id,
        companyName: 'Test Company',
        jobTitle: 'Software Developer',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        employmentType: 'full-time'
      });

      const response = await request(app)
        .get('/api/work-history')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.workHistory).toHaveLength(1);
      expect(response.body.data.workHistory[0].companyName).toBe('Test Company');
    });

    it('should not get work history without authentication', async () => {
      const response = await request(app)
        .get('/api/work-history')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/work-history', () => {
    it('should create new work history entry', async () => {
      const workHistoryData = {
        companyName: 'Test Company',
        jobTitle: 'Software Developer',
        department: 'Engineering',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        employmentType: 'full-time',
        description: 'Developed web applications',
        hrContactEmail: '<EMAIL>',
        hrContactName: 'HR Manager'
      };

      const response = await request(app)
        .post('/api/work-history')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(workHistoryData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.workHistory.companyName).toBe(workHistoryData.companyName);
      expect(response.body.data.workHistory.verificationStatus).toBe('pending');
    });

    it('should not create work history with invalid data', async () => {
      const workHistoryData = {
        // Missing required fields
        jobTitle: 'Software Developer'
      };

      const response = await request(app)
        .post('/api/work-history')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(workHistoryData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should not create work history without authentication', async () => {
      const workHistoryData = {
        companyName: 'Test Company',
        jobTitle: 'Software Developer',
        startDate: '2023-01-01'
      };

      const response = await request(app)
        .post('/api/work-history')
        .send(workHistoryData)
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/work-history/:id', () => {
    let workHistory;

    beforeEach(async () => {
      workHistory = await WorkHistory.create({
        userId: user.id,
        companyName: 'Test Company',
        jobTitle: 'Software Developer',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        employmentType: 'full-time'
      });
    });

    it('should update work history entry', async () => {
      const updateData = {
        jobTitle: 'Senior Software Developer',
        department: 'Engineering'
      };

      const response = await request(app)
        .put(`/api/work-history/${workHistory.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.workHistory.jobTitle).toBe(updateData.jobTitle);
    });

    it('should not update work history of another user', async () => {
      // Create another user
      const otherUser = await User.create({
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: 'password123'
      });

      const otherAccessToken = generateToken(otherUser.id);

      const response = await request(app)
        .put(`/api/work-history/${workHistory.id}`)
        .set('Authorization', `Bearer ${otherAccessToken}`)
        .send({ jobTitle: 'Hacker' })
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /api/work-history/:id', () => {
    let workHistory;

    beforeEach(async () => {
      workHistory = await WorkHistory.create({
        userId: user.id,
        companyName: 'Test Company',
        jobTitle: 'Software Developer',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        employmentType: 'full-time'
      });
    });

    it('should delete work history entry', async () => {
      const response = await request(app)
        .delete(`/api/work-history/${workHistory.id}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);

      // Verify deletion
      const deletedWorkHistory = await WorkHistory.findByPk(workHistory.id);
      expect(deletedWorkHistory).toBeNull();
    });

    it('should not delete work history of another user', async () => {
      // Create another user
      const otherUser = await User.create({
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: 'password123'
      });

      const otherAccessToken = generateToken(otherUser.id);

      const response = await request(app)
        .delete(`/api/work-history/${workHistory.id}`)
        .set('Authorization', `Bearer ${otherAccessToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);

      // Verify work history still exists
      const existingWorkHistory = await WorkHistory.findByPk(workHistory.id);
      expect(existingWorkHistory).not.toBeNull();
    });
  });
});
