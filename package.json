{"name": "verijob", "version": "1.0.0", "description": "VeriJob Phase 1 - Employment Verification Platform", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm start", "server:start": "cd server && npm start", "client:build": "cd client && npm run build", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test && cd ../client && npm test", "lint": "cd server && npm run lint && cd ../client && npm run lint"}, "keywords": ["employment", "verification", "hr", "background-check"], "author": "VeriJob Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}