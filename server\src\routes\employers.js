const express = require('express');
const router = express.Router();

// Import controllers
const {
  getEmployers,
  getEmployerById
} = require('../controllers/employerController');

// Import middleware
const { authenticateToken } = require('../middleware/auth');
const { validateUUID } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// @route   GET /api/employers
// @desc    Get employers (for autocomplete/search)
// @access  Private
router.get('/', getEmployers);

// @route   GET /api/employers/:id
// @desc    Get specific employer
// @access  Private
router.get('/:id', validateUUID('id'), getEmployerById);

module.exports = router;
