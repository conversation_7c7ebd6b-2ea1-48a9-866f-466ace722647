# VeriJob - Employment Verification Platform

## Phase 1 - Foundation Release

VeriJob is a comprehensive employment verification platform that streamlines the process of verifying work history for candidates and employers.

### Features

- **Employment Verification Engine**: Automated email-based verification workflow
- **Candidate Portal**: Secure profile management and work history tracking
- **Employer Integration**: Simple verification form system for HR departments
- **Admin Dashboard**: Pilot employer management and verification monitoring

### Tech Stack

- **Frontend**: React.js with Tailwind CSS
- **Backend**: Node.js with Express
- **Database**: PostgreSQL
- **Email Service**: SendGrid
- **Authentication**: JWT with bcrypt password hashing

### Project Structure

```
verijob/
├── client/                 # React frontend application
│   ├── public/
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API service functions
│   │   ├── utils/          # Utility functions
│   │   └── styles/         # CSS and styling
│   └── package.json
├── server/                 # Node.js backend API
│   ├── src/
│   │   ├── controllers/    # Route handlers
│   │   ├── middleware/     # Custom middleware
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── config/             # Configuration files
│   ├── migrations/         # Database migrations
│   └── package.json
├── docs/                   # Documentation
└── package.json           # Root package.json
```

### Getting Started

1. **Install Dependencies**
   ```bash
   npm run install:all
   ```

2. **Set up Environment Variables**
   - Copy `.env.example` to `.env` in both client and server directories
   - Configure database connection and email service credentials

3. **Run Database Migrations**
   ```bash
   cd server && npm run migrate
   ```

4. **Start Development Servers**
   ```bash
   npm run dev
   ```

### Security Features

- HTTPS-only verification forms
- CSRF protection
- Input sanitization
- JWT-based authentication
- Password hashing with bcrypt
- Secure verification link expiration (7 days)

### Environment Setup

#### Prerequisites
- Node.js 18+ 
- PostgreSQL 14+
- SendGrid account for email service

#### Development
- Frontend runs on http://localhost:3000
- Backend API runs on http://localhost:5000
- Database runs on localhost:5432

### API Documentation

API documentation will be available at `/api/docs` when the server is running.

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### License

MIT License - see LICENSE file for details.
