const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');

const WorkHistory = sequelize.define('WorkHistory', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  employerId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'employers',
      key: 'id'
    }
  },
  companyName: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [1, 100]
    }
  },
  jobTitle: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: [1, 100]
    }
  },
  department: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [1, 50]
    }
  },
  startDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    validate: {
      isDate: true
    }
  },
  endDate: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    validate: {
      isDate: true,
      isAfterStartDate(value) {
        if (value && this.startDate && new Date(value) <= new Date(this.startDate)) {
          throw new Error('End date must be after start date');
        }
      }
    }
  },
  isCurrentJob: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  employmentType: {
    type: DataTypes.ENUM('full-time', 'part-time', 'contract', 'internship', 'freelance'),
    defaultValue: 'full-time'
  },
  salary: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  salaryType: {
    type: DataTypes.ENUM('hourly', 'monthly', 'yearly'),
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  hrContactEmail: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  hrContactName: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [1, 100]
    }
  },
  supervisorName: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [1, 100]
    }
  },
  supervisorEmail: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  verificationStatus: {
    type: DataTypes.ENUM('pending', 'verified', 'not_verified', 'expired'),
    defaultValue: 'pending'
  },
  verificationRequestedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  verificationCompletedAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'work_history',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['employer_id']
    },
    {
      fields: ['verification_status']
    },
    {
      fields: ['is_current_job']
    }
  ],
  hooks: {
    beforeSave: (workHistory) => {
      if (workHistory.isCurrentJob) {
        workHistory.endDate = null;
      }
    }
  }
});

module.exports = WorkHistory;
