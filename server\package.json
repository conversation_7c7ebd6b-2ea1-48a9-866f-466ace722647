{"name": "verijob-server", "version": "1.0.0", "description": "VeriJob Backend API Server", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "migrate": "node src/utils/migrate.js", "seed": "node src/utils/seed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "@sendgrid/mail": "^8.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "uuid": "^9.0.1", "dotenv": "^16.3.1", "morgan": "^1.10.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "csurf": "^1.11.0", "isomorphic-dompurify": "^2.8.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.56.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0"}, "engines": {"node": ">=18.0.0"}}