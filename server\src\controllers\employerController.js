const { Employer } = require('../models');
const { Op } = require('sequelize');

// @desc    Get employers (for autocomplete/search)
// @route   GET /api/employers
// @access  Private
const getEmployers = async (req, res, next) => {
  try {
    const { search, limit = 10 } = req.query;

    const whereClause = {
      isActive: true
    };

    if (search) {
      whereClause.companyName = {
        [Op.iLike]: `%${search}%`
      };
    }

    const employers = await Employer.findAll({
      where: whereClause,
      attributes: ['id', 'companyName', 'hrContactEmail', 'industry', 'isPilotTester'],
      order: [['companyName', 'ASC']],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: { employers }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get specific employer
// @route   GET /api/employers/:id
// @access  Private
const getEmployerById = async (req, res, next) => {
  try {
    const employer = await Employer.findOne({
      where: {
        id: req.params.id,
        isActive: true
      }
    });

    if (!employer) {
      return res.status(404).json({
        success: false,
        message: 'Employer not found'
      });
    }

    res.json({
      success: true,
      data: { employer }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getEmployers,
  getEmployerById
};
