const { sequelize } = require('../../config/database');
const User = require('./User');
const Employer = require('./Employer');
const WorkHistory = require('./WorkHistory');
const Verification = require('./Verification');

// Define associations
User.hasMany(WorkHistory, {
  foreignKey: 'userId',
  as: 'workHistory'
});

WorkHistory.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

Employer.hasMany(WorkHistory, {
  foreignKey: 'employerId',
  as: 'workHistory'
});

WorkHistory.belongsTo(Employer, {
  foreignKey: 'employerId',
  as: 'employer'
});

WorkHistory.hasMany(Verification, {
  foreignKey: 'workHistoryId',
  as: 'verifications'
});

Verification.belongsTo(WorkHistory, {
  foreignKey: 'workHistoryId',
  as: 'workHistory'
});

Employer.hasMany(Verification, {
  foreignKey: 'employerId',
  as: 'verifications'
});

Verification.belongsTo(Employer, {
  foreignKey: 'employerId',
  as: 'employer'
});

// Sync database
const syncDatabase = async (force = false) => {
  try {
    await sequelize.sync({ force });
    console.log('Database synchronized successfully.');
  } catch (error) {
    console.error('Error synchronizing database:', error);
    throw error;
  }
};

module.exports = {
  sequelize,
  User,
  Employer,
  WorkHistory,
  Verification,
  syncDatabase
};
