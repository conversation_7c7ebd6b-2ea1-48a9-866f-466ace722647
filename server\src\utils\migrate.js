const { syncDatabase, sequelize } = require('../models');
const { testConnection } = require('../../config/database');

const runMigrations = async () => {
  try {
    console.log('Starting database migration...');

    // Test connection first
    await testConnection();

    // Sync database (create tables)
    await syncDatabase(false); // Set to true to drop and recreate tables

    console.log('Database migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
};

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations();
}

module.exports = { runMigrations };
