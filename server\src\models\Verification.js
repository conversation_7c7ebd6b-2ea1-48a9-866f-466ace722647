const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');
const crypto = require('crypto');

const Verification = sequelize.define('Verification', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  workHistoryId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'work_history',
      key: 'id'
    }
  },
  employerId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'employers',
      key: 'id'
    }
  },
  token: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  emailSentTo: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isEmail: true
    }
  },
  emailSentAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('sent', 'opened', 'completed', 'expired'),
    defaultValue: 'sent'
  },
  // Verification form responses
  employmentConfirmed: {
    type: DataTypes.BOOLEAN,
    allowNull: true
  },
  verifiedJobTitle: {
    type: DataTypes.STRING,
    allowNull: true
  },
  verifiedStartDate: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  verifiedEndDate: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  verifiedEmploymentType: {
    type: DataTypes.ENUM('full-time', 'part-time', 'contract', 'internship', 'freelance'),
    allowNull: true
  },
  verifierName: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [1, 100]
    }
  },
  verifierTitle: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: [1, 100]
    }
  },
  verifierEmail: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  comments: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  discrepancies: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  ipAddress: {
    type: DataTypes.INET,
    allowNull: true
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  attemptCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  lastAttemptAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'verifications',
  indexes: [
    {
      unique: true,
      fields: ['token']
    },
    {
      fields: ['work_history_id']
    },
    {
      fields: ['employer_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['expires_at']
    }
  ],
  hooks: {
    beforeCreate: (verification) => {
      if (!verification.token) {
        verification.token = crypto.randomBytes(32).toString('hex');
      }
      if (!verification.expiresAt) {
        const expiryDays = parseInt(process.env.VERIFICATION_LINK_EXPIRES_DAYS) || 7;
        verification.expiresAt = new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000);
      }
    }
  }
});

// Instance methods
Verification.prototype.isExpired = function() {
  return new Date() > this.expiresAt;
};

Verification.prototype.markAsOpened = async function() {
  if (this.status === 'sent') {
    this.status = 'opened';
    await this.save();
  }
};

Verification.prototype.incrementAttempt = async function() {
  this.attemptCount += 1;
  this.lastAttemptAt = new Date();
  await this.save();
};

module.exports = Verification;
