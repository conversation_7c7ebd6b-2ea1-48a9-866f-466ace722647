{"name": "verijob-client", "version": "1.0.0", "description": "VeriJob Frontend React Application", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "axios": "^1.6.2", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "js-cookie": "^3.0.5", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "classnames": "^2.3.2"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "eslint": "^8.56.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}