const express = require('express');
const router = express.Router();

// Import controllers
const {
  getDashboard,
  getEmployers,
  createEmployer,
  updateEmployer,
  deleteEmployer,
  getVerifications,
  getUsers
} = require('../controllers/adminController');

// Import middleware
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { validateEmployer, validateUUID } = require('../middleware/validation');

// All routes require admin authentication
router.use(authenticateToken, requireAdmin);

// @route   GET /api/admin/dashboard
// @desc    Get admin dashboard data
// @access  Admin
router.get('/dashboard', getDashboard);

// @route   GET /api/admin/employers
// @desc    Get all employers
// @access  Admin
router.get('/employers', getEmployers);

// @route   POST /api/admin/employers
// @desc    Create new employer
// @access  Admin
router.post('/employers', validateEmployer, createEmployer);

// @route   PUT /api/admin/employers/:id
// @desc    Update employer
// @access  Admin
router.put('/employers/:id', validateUUID('id'), validateEmployer, updateEmployer);

// @route   DELETE /api/admin/employers/:id
// @desc    Delete employer
// @access  Admin
router.delete('/employers/:id', validateUUID('id'), deleteEmployer);

// @route   GET /api/admin/verifications
// @desc    Get all verifications
// @access  Admin
router.get('/verifications', getVerifications);

// @route   GET /api/admin/users
// @desc    Get all users
// @access  Admin
router.get('/users', getUsers);

module.exports = router;
