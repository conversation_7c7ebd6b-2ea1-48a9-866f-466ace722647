const sgMail = require('@sendgrid/mail');
const fs = require('fs').promises;
const path = require('path');

// Initialize SendGrid
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

// Email templates
const EMAIL_TEMPLATES = {
  VERIFICATION_REQUEST: 'verification-request',
  WELCOME: 'welcome',
  PASSWORD_RESET: 'password-reset',
  EMAIL_VERIFICATION: 'email-verification'
};

class EmailService {
  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.fromName = process.env.FROM_NAME || 'VeriJob Verification System';
    this.clientBaseUrl = process.env.CLIENT_BASE_URL || 'http://localhost:3000';
    this.apiBaseUrl = process.env.API_BASE_URL || 'http://localhost:5000';
  }

  // Load email template
  async loadTemplate(templateName) {
    try {
      const templatePath = path.join(__dirname, '../templates/emails', `${templateName}.html`);
      return await fs.readFile(templatePath, 'utf8');
    } catch (error) {
      console.error(`Error loading email template ${templateName}:`, error);
      return this.getDefaultTemplate(templateName);
    }
  }

  // Get default template if file doesn't exist
  getDefaultTemplate(templateName) {
    switch (templateName) {
      case EMAIL_TEMPLATES.VERIFICATION_REQUEST:
        return this.getVerificationRequestTemplate();
      case EMAIL_TEMPLATES.WELCOME:
        return this.getWelcomeTemplate();
      case EMAIL_TEMPLATES.PASSWORD_RESET:
        return this.getPasswordResetTemplate();
      case EMAIL_TEMPLATES.EMAIL_VERIFICATION:
        return this.getEmailVerificationTemplate();
      default:
        return '<p>{{content}}</p>';
    }
  }

  // Replace template variables
  replaceTemplateVariables(template, variables) {
    let result = template;
    Object.keys(variables).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, variables[key] || '');
    });
    return result;
  }

  // Send verification request email to employer
  async sendVerificationRequest(verification, workHistory, candidate) {
    try {
      const template = await this.loadTemplate(EMAIL_TEMPLATES.VERIFICATION_REQUEST);
      
      const verificationUrl = `${this.clientBaseUrl}/verify/${verification.token}`;
      const expiryDate = new Date(verification.expiresAt).toLocaleDateString();
      
      const variables = {
        candidateName: candidate.getFullName(),
        candidateEmail: candidate.email,
        companyName: workHistory.companyName,
        jobTitle: workHistory.jobTitle,
        startDate: new Date(workHistory.startDate).toLocaleDateString(),
        endDate: workHistory.endDate ? new Date(workHistory.endDate).toLocaleDateString() : 'Present',
        verificationUrl,
        expiryDate,
        supportEmail: this.fromEmail
      };

      const htmlContent = this.replaceTemplateVariables(template, variables);

      const msg = {
        to: verification.emailSentTo,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject: `Employment Verification Request for ${candidate.getFullName()}`,
        html: htmlContent,
        text: this.generateTextVersion(variables)
      };

      const result = await sgMail.send(msg);
      console.log(`Verification email sent to ${verification.emailSentTo}`);
      return result;
    } catch (error) {
      console.error('Error sending verification email:', error);
      throw error;
    }
  }

  // Send welcome email to new user
  async sendWelcomeEmail(user) {
    try {
      const template = await this.loadTemplate(EMAIL_TEMPLATES.WELCOME);
      
      const variables = {
        firstName: user.firstName,
        dashboardUrl: `${this.clientBaseUrl}/dashboard`,
        supportEmail: this.fromEmail
      };

      const htmlContent = this.replaceTemplateVariables(template, variables);

      const msg = {
        to: user.email,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject: 'Welcome to VeriJob!',
        html: htmlContent
      };

      const result = await sgMail.send(msg);
      console.log(`Welcome email sent to ${user.email}`);
      return result;
    } catch (error) {
      console.error('Error sending welcome email:', error);
      throw error;
    }
  }

  // Send password reset email
  async sendPasswordResetEmail(user, resetToken) {
    try {
      const template = await this.loadTemplate(EMAIL_TEMPLATES.PASSWORD_RESET);
      
      const resetUrl = `${this.clientBaseUrl}/reset-password?token=${resetToken}`;
      
      const variables = {
        firstName: user.firstName,
        resetUrl,
        supportEmail: this.fromEmail
      };

      const htmlContent = this.replaceTemplateVariables(template, variables);

      const msg = {
        to: user.email,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject: 'Password Reset Request',
        html: htmlContent
      };

      const result = await sgMail.send(msg);
      console.log(`Password reset email sent to ${user.email}`);
      return result;
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw error;
    }
  }

  // Send email verification
  async sendEmailVerification(user, verificationToken) {
    try {
      const template = await this.loadTemplate(EMAIL_TEMPLATES.EMAIL_VERIFICATION);
      
      const verificationUrl = `${this.apiBaseUrl}/api/auth/verify-email/${verificationToken}`;
      
      const variables = {
        firstName: user.firstName,
        verificationUrl,
        supportEmail: this.fromEmail
      };

      const htmlContent = this.replaceTemplateVariables(template, variables);

      const msg = {
        to: user.email,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject: 'Verify Your Email Address',
        html: htmlContent
      };

      const result = await sgMail.send(msg);
      console.log(`Email verification sent to ${user.email}`);
      return result;
    } catch (error) {
      console.error('Error sending email verification:', error);
      throw error;
    }
  }

  // Generate text version of verification email
  generateTextVersion(variables) {
    return `
Employment Verification Request

Dear HR Team,

We have received a request to verify the employment history of ${variables.candidateName} (${variables.candidateEmail}).

Employment Details:
- Company: ${variables.companyName}
- Position: ${variables.jobTitle}
- Employment Period: ${variables.startDate} - ${variables.endDate}

To complete this verification, please click the following link:
${variables.verificationUrl}

This link will expire on ${variables.expiryDate}.

If you have any questions, please contact us at ${variables.supportEmail}.

Thank you for your cooperation.

Best regards,
VeriJob Verification Team
    `.trim();
  }

  // Default email templates
  getVerificationRequestTemplate() {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employment Verification Request</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #3b82f6; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .button { display: inline-block; background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Employment Verification Request</h1>
        </div>
        <div class="content">
            <p>Dear HR Team,</p>
            <p>We have received a request to verify the employment history of <strong>{{candidateName}}</strong> ({{candidateEmail}}).</p>
            
            <h3>Employment Details:</h3>
            <ul>
                <li><strong>Company:</strong> {{companyName}}</li>
                <li><strong>Position:</strong> {{jobTitle}}</li>
                <li><strong>Employment Period:</strong> {{startDate}} - {{endDate}}</li>
            </ul>
            
            <p>To complete this verification, please click the button below:</p>
            <a href="{{verificationUrl}}" class="button">Verify Employment</a>
            
            <p><strong>Important:</strong> This link will expire on {{expiryDate}}.</p>
            
            <p>If you have any questions, please contact us at {{supportEmail}}.</p>
            
            <p>Thank you for your cooperation.</p>
        </div>
        <div class="footer">
            <p>VeriJob Verification System</p>
        </div>
    </div>
</body>
</html>
    `;
  }

  getWelcomeTemplate() {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Welcome to VeriJob</title>
</head>
<body>
    <h1>Welcome to VeriJob, {{firstName}}!</h1>
    <p>Thank you for joining VeriJob. You can now start adding your work history and requesting employment verifications.</p>
    <a href="{{dashboardUrl}}">Go to Dashboard</a>
</body>
</html>
    `;
  }

  getPasswordResetTemplate() {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Password Reset</title>
</head>
<body>
    <h1>Password Reset Request</h1>
    <p>Hi {{firstName}},</p>
    <p>You requested a password reset. Click the link below to reset your password:</p>
    <a href="{{resetUrl}}">Reset Password</a>
    <p>If you didn't request this, please ignore this email.</p>
</body>
</html>
    `;
  }

  getEmailVerificationTemplate() {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Verify Your Email</title>
</head>
<body>
    <h1>Verify Your Email Address</h1>
    <p>Hi {{firstName}},</p>
    <p>Please click the link below to verify your email address:</p>
    <a href="{{verificationUrl}}">Verify Email</a>
</body>
</html>
    `;
  }
}

module.exports = new EmailService();
