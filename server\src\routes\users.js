const express = require('express');
const router = express.Router();

// Import controllers
const {
  getProfile,
  updateProfile,
  deleteAccount
} = require('../controllers/userController');

// Import middleware
const { authenticateToken } = require('../middleware/auth');

// All routes require authentication
router.use(authenticateToken);

// @route   GET /api/users/profile
// @desc    Get user profile
// @access  Private
router.get('/profile', getProfile);

// @route   PUT /api/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', updateProfile);

// @route   DELETE /api/users/profile
// @desc    Delete user account
// @access  Private
router.delete('/profile', deleteAccount);

module.exports = router;
