const { Verification, WorkHistory, User, Employer } = require('../models');

// @desc    Get verification form data
// @route   GET /api/verification/:token
// @access  Public
const getVerificationForm = async (req, res, next) => {
  try {
    const { token } = req.params;

    const verification = await Verification.findOne({
      where: { token },
      include: [
        {
          model: WorkHistory,
          as: 'workHistory',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['firstName', 'lastName', 'email']
            },
            {
              model: Employer,
              as: 'employer',
              attributes: ['companyName', 'hrContactEmail']
            }
          ]
        }
      ]
    });

    if (!verification) {
      return res.status(404).json({
        success: false,
        message: 'Verification form not found'
      });
    }

    // Check if verification is expired
    if (verification.isExpired()) {
      return res.status(410).json({
        success: false,
        message: 'Verification link has expired'
      });
    }

    // Check if already completed
    if (verification.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Verification has already been completed'
      });
    }

    // Mark as opened if first time
    await verification.markAsOpened();

    // Increment attempt count
    await verification.incrementAttempt();

    // Check max attempts
    const maxAttempts = parseInt(process.env.MAX_VERIFICATION_ATTEMPTS) || 3;
    if (verification.attemptCount > maxAttempts) {
      return res.status(429).json({
        success: false,
        message: 'Maximum verification attempts exceeded'
      });
    }

    const workHistory = verification.workHistory;

    res.json({
      success: true,
      data: {
        verification: {
          id: verification.id,
          token: verification.token,
          status: verification.status,
          emailSentAt: verification.emailSentAt,
          expiresAt: verification.expiresAt,
          attemptCount: verification.attemptCount
        },
        candidate: {
          name: workHistory.user.getFullName(),
          email: workHistory.user.email
        },
        employment: {
          companyName: workHistory.companyName,
          jobTitle: workHistory.jobTitle,
          department: workHistory.department,
          startDate: workHistory.startDate,
          endDate: workHistory.endDate,
          isCurrentJob: workHistory.isCurrentJob,
          employmentType: workHistory.employmentType,
          description: workHistory.description
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Submit verification form
// @route   POST /api/verification/:token
// @access  Public
const submitVerificationForm = async (req, res, next) => {
  try {
    const { token } = req.params;
    const {
      employmentConfirmed,
      verifiedJobTitle,
      verifiedStartDate,
      verifiedEndDate,
      verifiedEmploymentType,
      verifierName,
      verifierTitle,
      verifierEmail,
      comments,
      discrepancies
    } = req.body;

    const verification = await Verification.findOne({
      where: { token },
      include: [
        {
          model: WorkHistory,
          as: 'workHistory'
        }
      ]
    });

    if (!verification) {
      return res.status(404).json({
        success: false,
        message: 'Verification form not found'
      });
    }

    // Check if verification is expired
    if (verification.isExpired()) {
      return res.status(410).json({
        success: false,
        message: 'Verification link has expired'
      });
    }

    // Check if already completed
    if (verification.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'Verification has already been completed'
      });
    }

    // Update verification with form data
    await verification.update({
      employmentConfirmed,
      verifiedJobTitle,
      verifiedStartDate,
      verifiedEndDate,
      verifiedEmploymentType,
      verifierName,
      verifierTitle,
      verifierEmail,
      comments,
      discrepancies,
      status: 'completed',
      completedAt: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    // Update work history verification status
    const workHistory = verification.workHistory;
    workHistory.verificationStatus = employmentConfirmed ? 'verified' : 'not_verified';
    workHistory.verificationCompletedAt = new Date();
    await workHistory.save();

    // Update employer verification count if employer exists
    if (verification.employerId) {
      const employer = await Employer.findByPk(verification.employerId);
      if (employer) {
        await employer.incrementVerificationCount();
      }
    }

    res.json({
      success: true,
      message: 'Verification submitted successfully',
      data: {
        verificationStatus: workHistory.verificationStatus,
        employmentConfirmed
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get verification status for work history
// @route   GET /api/verification/status/:workHistoryId
// @access  Private
const getVerificationStatus = async (req, res, next) => {
  try {
    const { workHistoryId } = req.params;

    const workHistory = await WorkHistory.findOne({
      where: {
        id: workHistoryId,
        userId: req.user.id
      },
      include: [
        {
          model: Verification,
          as: 'verifications',
          order: [['createdAt', 'DESC']],
          limit: 1
        }
      ]
    });

    if (!workHistory) {
      return res.status(404).json({
        success: false,
        message: 'Work history not found'
      });
    }

    const latestVerification = workHistory.verifications[0];

    res.json({
      success: true,
      data: {
        workHistoryId: workHistory.id,
        verificationStatus: workHistory.verificationStatus,
        verificationRequestedAt: workHistory.verificationRequestedAt,
        verificationCompletedAt: workHistory.verificationCompletedAt,
        latestVerification: latestVerification ? {
          id: latestVerification.id,
          status: latestVerification.status,
          emailSentAt: latestVerification.emailSentAt,
          completedAt: latestVerification.completedAt,
          employmentConfirmed: latestVerification.employmentConfirmed,
          verifierName: latestVerification.verifierName,
          comments: latestVerification.comments
        } : null
      }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getVerificationForm,
  submitVerificationForm,
  getVerificationStatus
};
