# Server Configuration
NODE_ENV=development
PORT=5000
API_BASE_URL=http://localhost:5000
CLIENT_BASE_URL=http://localhost:3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=verijob_dev
DB_USER=postgres
DB_PASSWORD=your_password_here

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# Email Configuration (SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key_here
FROM_EMAIL=<EMAIL>
FROM_NAME=VeriJob Verification System

# Security
SESSION_SECRET=your_session_secret_here
CSRF_SECRET=your_csrf_secret_here

# Verification Settings
VERIFICATION_LINK_EXPIRES_DAYS=7
MAX_VERIFICATION_ATTEMPTS=3

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
