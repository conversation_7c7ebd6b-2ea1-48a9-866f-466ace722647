import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { 
  UsersIcon, 
  BuildingOfficeIcon, 
  DocumentCheckIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const AdminDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await axios.get('/api/admin/dashboard');
      setDashboardData(response.data.data);
    } catch (error) {
      toast.error('Failed to load dashboard data');
      console.error('Dashboard error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'sent':
      case 'opened':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'expired':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'sent':
        return 'Sent';
      case 'opened':
        return 'Opened';
      case 'expired':
        return 'Expired';
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="mt-2 text-gray-600">
          Monitor and manage the VeriJob platform.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UsersIcon className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData?.stats.totalUsers || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BuildingOfficeIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Employers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData?.stats.totalEmployers || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentCheckIcon className="h-8 w-8 text-purple-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Verifications</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData?.stats.totalVerifications || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-8 w-8 text-yellow-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending Verifications</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData?.stats.pendingVerifications || 0}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-4">
          <Link
            to="/admin/employers"
            className="btn-primary inline-flex items-center"
          >
            <BuildingOfficeIcon className="h-5 w-5 mr-2" />
            Manage Employers
          </Link>
          <Link
            to="/admin/verifications"
            className="btn-outline inline-flex items-center"
          >
            <DocumentCheckIcon className="h-5 w-5 mr-2" />
            View Verifications
          </Link>
          <Link
            to="/admin/users"
            className="btn-outline inline-flex items-center"
          >
            <UsersIcon className="h-5 w-5 mr-2" />
            Manage Users
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Verifications */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-medium text-gray-900">Recent Verifications</h2>
          </div>
          <div className="card-body p-0">
            {dashboardData?.recentVerifications?.length === 0 ? (
              <div className="text-center py-8">
                <DocumentCheckIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No verifications yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Verifications will appear here as they are submitted.
                </p>
              </div>
            ) : (
              <div className="overflow-hidden">
                <ul className="divide-y divide-gray-200">
                  {dashboardData?.recentVerifications?.map((verification) => (
                    <li key={verification.id} className="px-6 py-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 mr-3">
                              {getStatusIcon(verification.status)}
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {verification.workHistory?.user?.firstName} {verification.workHistory?.user?.lastName}
                              </p>
                              <p className="text-sm text-gray-500 truncate">
                                {verification.workHistory?.jobTitle} at {verification.employer?.companyName}
                              </p>
                              <p className="text-xs text-gray-400">
                                {new Date(verification.emailSentAt).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            verification.status === 'completed' 
                              ? 'bg-green-100 text-green-800'
                              : verification.status === 'expired'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {getStatusText(verification.status)}
                          </span>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        {/* Verification Stats */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-medium text-gray-900">Verification Statistics</h2>
          </div>
          <div className="card-body">
            {dashboardData?.verificationStats?.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-sm text-gray-500">No verification data available</p>
              </div>
            ) : (
              <div className="space-y-4">
                {dashboardData?.verificationStats?.map((stat) => (
                  <div key={stat.status} className="flex items-center justify-between">
                    <div className="flex items-center">
                      {getStatusIcon(stat.status)}
                      <span className="ml-2 text-sm font-medium text-gray-900 capitalize">
                        {getStatusText(stat.status)}
                      </span>
                    </div>
                    <span className="text-sm font-bold text-gray-900">
                      {stat.count}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* System Status */}
      <div className="mt-8">
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-medium text-gray-900">System Status</h2>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">API Status</p>
                  <p className="text-sm text-gray-500">Operational</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">Email Service</p>
                  <p className="text-sm text-gray-500">Operational</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-900">Database</p>
                  <p className="text-sm text-gray-500">Operational</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
