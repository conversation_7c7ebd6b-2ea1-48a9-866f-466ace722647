const { User, Employer, WorkHistory, Verification } = require('../models');
const { Op } = require('sequelize');

// @desc    Get admin dashboard data
// @route   GET /api/admin/dashboard
// @access  Admin
const getDashboard = async (req, res, next) => {
  try {
    // Get counts
    const totalUsers = await User.count({ where: { role: 'candidate' } });
    const totalEmployers = await Employer.count();
    const totalVerifications = await Verification.count();
    const pendingVerifications = await Verification.count({ where: { status: 'sent' } });
    const completedVerifications = await Verification.count({ where: { status: 'completed' } });

    // Get recent activity
    const recentVerifications = await Verification.findAll({
      limit: 10,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: WorkHistory,
          as: 'workHistory',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['firstName', 'lastName', 'email']
            }
          ]
        },
        {
          model: Employer,
          as: 'employer',
          attributes: ['companyName']
        }
      ]
    });

    // Get verification stats by status
    const verificationStats = await Verification.findAll({
      attributes: [
        'status',
        [require('sequelize').fn('COUNT', require('sequelize').col('status')), 'count']
      ],
      group: ['status']
    });

    res.json({
      success: true,
      data: {
        stats: {
          totalUsers,
          totalEmployers,
          totalVerifications,
          pendingVerifications,
          completedVerifications
        },
        recentVerifications,
        verificationStats
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get all employers
// @route   GET /api/admin/employers
// @access  Admin
const getEmployers = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, search, pilotOnly } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};

    if (search) {
      whereClause[Op.or] = [
        { companyName: { [Op.iLike]: `%${search}%` } },
        { hrContactEmail: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (pilotOnly === 'true') {
      whereClause.isPilotTester = true;
    }

    const employers = await Employer.findAndCountAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        employers: employers.rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(employers.count / limit),
          totalItems: employers.count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new employer
// @route   POST /api/admin/employers
// @access  Admin
const createEmployer = async (req, res, next) => {
  try {
    const employer = await Employer.create(req.body);

    res.status(201).json({
      success: true,
      message: 'Employer created successfully',
      data: { employer }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update employer
// @route   PUT /api/admin/employers/:id
// @access  Admin
const updateEmployer = async (req, res, next) => {
  try {
    const employer = await Employer.findByPk(req.params.id);

    if (!employer) {
      return res.status(404).json({
        success: false,
        message: 'Employer not found'
      });
    }

    await employer.update(req.body);

    res.json({
      success: true,
      message: 'Employer updated successfully',
      data: { employer }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete employer
// @route   DELETE /api/admin/employers/:id
// @access  Admin
const deleteEmployer = async (req, res, next) => {
  try {
    const employer = await Employer.findByPk(req.params.id);

    if (!employer) {
      return res.status(404).json({
        success: false,
        message: 'Employer not found'
      });
    }

    // Soft delete
    await employer.update({ isActive: false });

    res.json({
      success: true,
      message: 'Employer deactivated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get all verifications
// @route   GET /api/admin/verifications
// @access  Admin
const getVerifications = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, status, employerId } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};

    if (status) {
      whereClause.status = status;
    }

    if (employerId) {
      whereClause.employerId = employerId;
    }

    const verifications = await Verification.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: WorkHistory,
          as: 'workHistory',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['firstName', 'lastName', 'email']
            }
          ]
        },
        {
          model: Employer,
          as: 'employer',
          attributes: ['companyName', 'hrContactEmail']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        verifications: verifications.rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(verifications.count / limit),
          totalItems: verifications.count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get all users
// @route   GET /api/admin/users
// @access  Admin
const getUsers = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, search, role } = req.query;
    const offset = (page - 1) * limit;

    const whereClause = {};

    if (search) {
      whereClause[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }

    if (role) {
      whereClause.role = role;
    }

    const users = await User.findAndCountAll({
      where: whereClause,
      attributes: { exclude: ['password'] },
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        users: users.rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(users.count / limit),
          totalItems: users.count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getDashboard,
  getEmployers,
  createEmployer,
  updateEmployer,
  deleteEmployer,
  getVerifications,
  getUsers
};
