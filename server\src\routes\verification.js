const express = require('express');
const router = express.Router();

// Import controllers
const {
  getVerificationForm,
  submitVerificationForm,
  getVerificationStatus
} = require('../controllers/verificationController');

// Import middleware
const { authenticateToken, optionalAuth } = require('../middleware/auth');
const { validateVerificationForm, validateUUID } = require('../middleware/validation');
const { verificationLimiter } = require('../middleware/security');

// @route   GET /api/verification/:token
// @desc    Get verification form (public access via token)
// @access  Public
router.get('/:token', getVerificationForm);

// @route   POST /api/verification/:token
// @desc    Submit verification form
// @access  Public
router.post('/:token', verificationLimiter, validateVerificationForm, submitVerificationForm);

// @route   GET /api/verification/status/:workHistoryId
// @desc    Get verification status for work history
// @access  Private
router.get('/status/:workHistoryId', authenticateToken, validateUUID('workHistoryId'), getVerificationStatus);

module.exports = router;
