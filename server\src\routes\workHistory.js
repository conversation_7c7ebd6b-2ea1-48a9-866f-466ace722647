const express = require('express');
const router = express.Router();

// Import controllers
const {
  getWorkHistory,
  createWorkHistory,
  updateWorkHistory,
  deleteWorkHistory,
  getWorkHistoryById
} = require('../controllers/workHistoryController');

// Import middleware
const { authenticateToken } = require('../middleware/auth');
const { validateWorkHistory, validateUUID } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// @route   GET /api/work-history
// @desc    Get user's work history
// @access  Private
router.get('/', getWorkHistory);

// @route   GET /api/work-history/:id
// @desc    Get specific work history entry
// @access  Private
router.get('/:id', validateUUID('id'), getWorkHistoryById);

// @route   POST /api/work-history
// @desc    Create new work history entry
// @access  Private
router.post('/', validateWorkHistory, createWorkHistory);

// @route   PUT /api/work-history/:id
// @desc    Update work history entry
// @access  Private
router.put('/:id', validateUUID('id'), validateWorkHistory, updateWorkHistory);

// @route   DELETE /api/work-history/:id
// @desc    Delete work history entry
// @access  Private
router.delete('/:id', validateUUID('id'), deleteWorkHistory);

module.exports = router;
